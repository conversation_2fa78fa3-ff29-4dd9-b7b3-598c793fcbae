import json

from loguru import logger

logger.level("USER", no=24, color="<yellow>", icon="🧑")
logger.level("TOOL", no=25, color="<magenta>", icon="🔧")
logger.level("ASSISTANT", no=26, color="<green>", icon="💬")

def custom_message_log(message):
    if isinstance(message, dict):
        cp = message.copy()
        role = cp.pop("role")
        logger.log(role.upper(), cp)
    else:
        try:
            msg = message.model_dump()
            logger.info(json.dumps(msg, indent=2, ensure_ascii=False))
            if 'content' in msg:
                logger.debug(msg['content'])
        except Exception as e:
            logger.error(f'Error logging message: {e}')
            logger.info(f'{message = }')