import os
from contextlib import asynccontextmanager
from functools import lru_cache, partial
from typing import Annotated, <PERSON><PERSON>

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Request
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.runnables import <PERSON><PERSON><PERSON>Lambda
from langchain_core.tools import BaseTool
from langfuse.callback import Callback<PERSON>andler
from langgraph.graph.state import CompiledStateGraph
from loguru import logger

from agents.dreamer2.common import config
from agents.dreamer2.src.agent import Dreamer2
from agents.dreamer2.src.internal_planner.internal_planner import (
    internal_planner,
)
from agents.dreamer2.src.models import DreamerResponse, Prompt
from agents.dreamer2.src.workflow_builder import build_workflow
from common.inference_client import InferenceClient
from common.langfuse.langfuse_handler import <PERSON><PERSON><PERSON>andler
from common.mcp_client import MCPClient


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Agent starting up...")
    config_dict = config.get_config_dict()
    logger.info("Configuration loaded: {}", config_dict)

    llm = InferenceClient(
        model_name=config_dict["model"]["dreamer2_model_id"],
        generation_config=config_dict["generation"],
    ).llm
    langfuse_callback = LangfuseHandler().get_langfuse_callback()
    mcp_client = MCPClient()
    mcp_tools = tuple()

    try:
        logger.info("Attempting to load MCP tools...")
        loaded_tools = await mcp_client.get_tools()
        mcp_tools = tuple(loaded_tools)
        logger.info(f"Successfully loaded {len(mcp_tools)} MCP tools.")
    except Exception as e:
        logger.warning(
            "Could not load MCP tools: {}. The agent will continue without them.", e
        )

    app.state.llm = llm
    app.state.langfuse_callback = langfuse_callback
    app.state.mcp_tools = mcp_tools
    app.state.user_guide_tools = tuple()  # placeholder for future tools

    yield

    logger.info("Agent shutting down...")


def get_llm(request: Request) -> BaseChatModel:
    return request.app.state.llm


def get_langfuse_callback(request: Request) -> CallbackHandler:
    return request.app.state.langfuse_callback


def get_mcp_tools(request: Request) -> Tuple[BaseTool, ...]:
    return request.app.state.mcp_tools


def get_user_guide_tools(request: Request) -> Tuple[BaseTool, ...]:
    return request.app.state.user_guide_tools


@lru_cache(maxsize=1)
def get_runnable_internal_planner(
    llm: Annotated[BaseChatModel, Depends(get_llm)],
    mcp_tools: Annotated[Tuple[BaseTool, ...], Depends(get_mcp_tools)],
    langfuse_callback: Annotated[CallbackHandler, Depends(get_langfuse_callback)],
) -> RunnableLambda:
    tools_list = list(mcp_tools)
    partial_planner = partial(
        internal_planner, tools=tools_list, llm=llm, langfuse_callback=langfuse_callback
    )
    return RunnableLambda(partial_planner)


@lru_cache(maxsize=1)
def get_runnable_user_guide(
    llm: Annotated[BaseChatModel, Depends(get_llm)],
    user_guide_tools: Annotated[Tuple[BaseTool, ...], Depends(get_user_guide_tools)],
    langfuse_callback: Annotated[CallbackHandler, Depends(get_langfuse_callback)],
) -> RunnableLambda:
    return RunnableLambda(lambda x: x)  # placeholder


@lru_cache(maxsize=1)
def get_compiled_workflow(
    runnable_internal_planner: Annotated[
        RunnableLambda, Depends(get_runnable_internal_planner)
    ],
    runnable_user_guide: Annotated[RunnableLambda, Depends(get_runnable_user_guide)],
    llm: Annotated[BaseChatModel, Depends(get_llm)],
) -> CompiledStateGraph:
    return build_workflow(
        runnable_internal_planner, runnable_user_guide, llm
        )


def get_dreamer2_agent(
    compiled_workflow: Annotated[CompiledStateGraph, Depends(get_compiled_workflow)],
) -> Dreamer2:
    return Dreamer2(compiled_workflow)


app = FastAPI(lifespan=lifespan)


@app.get("/health", status_code=200)
async def health():
    """Health check endpoint to confirm the service is running."""
    return {"status": "ok"}

# Placeholder for user guide content loaded from S3
# In a production environment, this would involve actual S3/lifespan fetch logic
# For demonstration, we'll use a dummy dictionary.
user_guide_content_data = {
    "Introduction": {
        "Overview": "This section provides an overview of the Dreamer2 agent."
    },
    "Getting Started": {
        "Installation": "To install Dreamer2, please follow these steps..."
    },
    "Features": {
        "Tool Usage": "Dreamer2 can utilize various tools to extend its capabilities."
    }
}

# Instantiate UserGuideTools once at application startup
user_guide_tools_instance = UserGuideTools(user_guide_content=user_guide_content_data)

@app.post("/predict/")
async def prompt(
    p: Prompt,
    dreamer2_agent: Annotated[Dreamer2, Depends(get_dreamer2_agent)],
    langfuse_callback: Annotated[CallbackHandler, Depends(get_langfuse_callback)],
) -> DreamerResponse:
    logger.info("Received new prompt for processing.")
    try:
        response = dreamer2_agent.run(p, langfuse_callback)
        logger.success("Successfully processed prompt.")
        return response
    except Exception as e:
        logger.error("An error occurred during prompt processing: {}", e)
        raise HTTPException(status_code=500, detail="Internal Server Error")


if __name__ == "__main__":
    run_config = config.get_config_dict()
    port = run_config["service"]["api_port"]
    log_level = run_config["service"]["log_level"]
    logger.info("Starting Uvicorn server on port {}.", port)
    uvicorn.run("main:app", host="0.0.0.0", port=port, log_level=log_level)
