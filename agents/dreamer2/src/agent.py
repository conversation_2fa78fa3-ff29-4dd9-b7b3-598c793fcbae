from agents.dreamer2.src.main_planner.prompts.prompts import policy
from agents.dreamer2.src.main_planner.main import react_agent
from common.langfuse.langfuse_handler import <PERSON><PERSON><PERSON>andler
from agents.dreamer2.src.models import Prompt, DreamerResponse, CLMResponse
from common.models.base_agent import BaseAgent
from loguru import logger
from langgraph.graph.state import CompiledStateGraph
from langfuse.callback import CallbackHandler


langfuse_callback = LangfuseHandler().get_langfuse_callback()


class Dreamer2(BaseAgent):

    def __init__(self, compiled_workflow: CompiledStateGraph):
        self.compiled_workflow = compiled_workflow

    def run(self, input_data: Prompt, langfuse_callback: Callback<PERSON>andler) -> DreamerResponse:
        # TODO: implement workflow invocation
        messages = [{"role": "system", "content": policy}]
        # messages.extend(input_data.session_history)
        messages.append({"role": "user", "content": input_data.prompt})  # TODO: history of tool calls need to be added
        # TODO: truncate messages to fit into context size
        for chunk in react_agent.stream({"messages": messages}, stream_mode="updates",
                                        config={"callbacks": [langfuse_callback]}):
            message = chunk[list(chunk.keys())[0]].get("messages")[0]
            logger.info(
                f"Message for org {input_data.organization_id}: \n {message.pretty_repr()}")

        return DreamerResponse(
            clmResponse=CLMResponse(
                message=message.content,
            )
        )

if __name__ == '__main__':
    # This part needs to be updated to reflect the new Dreamer2.__init__ signature
    # For local testing, you might need to create a dummy UserGuideTools instance here.
    # For now, I'll comment it out to prevent immediate errors, but this will need attention.
    # p = Prompt(prompt="where i can add new organization and upliad files for new discovery?", organization_id="bla", session_history=[])
    # Dreamer2().run(p)
    pass