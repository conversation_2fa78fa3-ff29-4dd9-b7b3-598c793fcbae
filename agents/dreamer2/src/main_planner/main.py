from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from agents.dreamer2.src.main_planner.tools import internal_planner
from langchain_core.tools import BaseTool
from typing import List

MODEL_NAME = "Qwen/Qwen2.5-32B-Instruct"

model_url = f'https://inference-dispatcher-prod.dreamlabs.songo-cloud.com/inference/{MODEL_NAME}/v1'

# TODO: wrap this shit
llm = init_chat_model(
    "any-name-can-work-here",
    model_provider="openai",
    base_url=model_url,
    api_key="dummy",
    max_tokens=200,
    temperature=0
)

# TODO: consider direct invocation instead of tool calling
tools = [user_guide, internal_planner]

react_agent = create_react_agent(model=llm, tools=tools)
