import json
import pathlib
import re

from docx import Document


def insert_section(stack, level, heading, _content, root):
    section = {"content": _content, "subsections": {}}

    # Pop stack to correct parent level
    while stack and stack[-1][0] >= level:
        stack.pop()

    # Insert into correct parent
    parent_dict = stack[-1][1] if stack else root
    parent_dict[heading] = section
    stack.append((level, section["subsections"]))


def parse_docx_to_json(path):
    doc = Document(path)
    root = {}

    stack = []  # Stack of (level, subsections_dict)
    current_heading = None
    current_level = None
    current_content = []

    for paragraph in doc.paragraphs:
        text = paragraph.text.strip()
        if not text:
            continue

        style = paragraph.style.name

        if style.startswith("Heading"):
            level = int(style.replace("Heading ", ""))

            if current_heading is not None:
                insert_section(stack, current_level, current_heading, " ".join(current_content), root=root)

            current_heading = text
            current_level = level
            current_content = []
        else:
            current_content.append(text)

    # Insert last section
    if current_heading is not None:
        insert_section(stack, current_level, current_heading, " ".join(current_content), root=root)

    return root


def flatten_dict(nested_dict):
    flattened_dict = {}

    for level1_title, level1_content in nested_dict.items():
        level1_section = {}

        for level2_title, level2_content in level1_content.get("subsections", {}).items():
            section_content = level2_content.get("content", "")
            subsections = level2_content.get("subsections", {})

            # Flatten deeper levels into markdown
            md_parts = [section_content] if section_content else []

            def collect_markdown(subs, depth):
                for sub_key, sub_val in subs.items():
                    header = "#" * (depth + 3) + f" {sub_key}"
                    _content = sub_val.get("content", "")
                    md_parts.append(f"{header}\n{_content}")
                    collect_markdown(sub_val.get("subsections", {}), depth + 1)

            collect_markdown(subsections, 0)
            full_content = "\n\n".join(filter(None, md_parts))

            level1_section[level2_title] = full_content

        flattened_dict[level1_title] = level1_section

    return flattened_dict


def normalize_section_name(section_name):
    # Keep only capitalized words
    words = [word for word in section_name.split() if word == word.capitalize()]
    # Join words with spaces
    cleaned = ' '.join(words)
    # Replace ' - ' with '__'
    cleaned = cleaned.replace(' - ', '__')
    # Remove unwanted characters using regex
    cleaned = re.sub(r'[ \-.,:;?!/\\*()\[\]{}|]', '', cleaned)
    return cleaned


def get_section_names(data: dict):
    _section_names = []
    for section_name, subsection_dict in data.items():
        for subsection_name, subsection_content in subsection_dict.items():
            _section_names.append(f"{section_name} - {subsection_name}")
    _section_names = {normalize_section_name(section_name): section_name for section_name
                      in _section_names}

    return _section_names


def prepare_data():
    # TODO: implement function to update this file from S3
    local_filepath = (pathlib.Path(__file__).resolve().parent / "UserGuide.docx").expanduser().resolve()
    msg = """
    download user guide file from https://dreamgroups.sharepoint.com/:w:/r/sites/Dream_All/Shared%20Documents/Customer%20Facing%20Documents/User%20Guides/User%20Guide/Latest%20User%20Guide/Dream%20Security%20-%20UG%2005-25.docx?d=wb1ac48298e484285b41a36a978e1cc5d&csf=1&web=1&e=VdpMLn
    """
    if not local_filepath.exists():
        raise FileNotFoundError(f"User guide file not found at {local_filepath}. {msg}")
    data = flatten_dict(parse_docx_to_json(str(local_filepath)))
    _section_names = get_section_names(data)
    return data, _section_names


if __name__ == "__main__":

    user_guide_content, section_names = prepare_data()
    json_filepath = (pathlib.Path(__file__).resolve().parent / "user_guide_content.json").expanduser().resolve()
    with open(json_filepath, "w") as f:
        json.dump(user_guide_content, f)

    for k, v in section_names.items():
        print(f"{k} -> {v}")
