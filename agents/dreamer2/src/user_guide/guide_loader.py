from functools import lru_cache
import json
import re
import boto3


BUCKET_NAME = "user-guide-dreamer"
KEY = "user_guide_content.json"


def get_section_names(data: dict):
    _section_names = []
    for section_name, subsection_dict in data.items():
        for subsection_name in subsection_dict.keys():
            _section_names.append(f"{section_name} - {subsection_name}")
    return _section_names

@lru_cache(maxsize=1)
def load_guide() -> dict[str, dict[str, str]]:
    s3 = boto3.client("s3")
    obj = s3.get_object(Bucket=BUCKET_NAME, Key=KEY)
    return json.loads(obj["Body"].read())

@lru_cache(maxsize=1)
def section_names() -> list[str]:
    user_guide_content = load_guide()
    return get_section_names(user_guide_content)
    

