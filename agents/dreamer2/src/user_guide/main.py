from langgraph.prebuilt import create_react_agent
from agents.dreamer2.src.user_guide.prompts.prompts import system_prompt
from langchain_core.tools import BaseTool
from langchain_core.language_models import BaseChatModel


def user_guide(user_prompt: str, llm: BaseChatModel, tools: list[BaseTool]) -> str:
    agent = create_react_agent(model=llm, tools=tools, name="user_guide_agent")
    
    messages = [{"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}]
    for chunk in agent.stream({"messages": messages}, stream_mode="updates"):
        message = chunk[list(chunk.keys())[0]].get("messages")[0]
        yield message.content
