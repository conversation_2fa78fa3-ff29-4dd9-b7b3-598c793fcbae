from enum import Enum
from agents.dreamer2.src.user_guide.guide_loader import load_guide, section_names
from utils import custom_message_log
from langchain_core.tools import tool


@tool()
def read_section(section_name: str) -> str:
    """
    Provide a section name to retrieve the content of the user guide. content of the user guide should be useful for answering the user question.

    :param section_name: SectionName
    :return: str: content of the section
    """
    user_guide_content = load_guide()
    section_names = section_names()

    assert section_name in section_names, f"Section {section_name} not found"
    
    custom_message_log(f'Tool called: read_section with {section_name = }')

    try:
        section_name, subsection_name = section_name.split(" - ")
        out = user_guide_content[section_name][subsection_name]
    except KeyError:
        raise ValueError(f"Section {section_name} - {subsection_name} not found")
    custom_message_log(f'Tool output: {out = }')
    return out
