import os
import tempfile
from typing import Any, Dict, Optional, <PERSON><PERSON>

import boto3
from mypy_boto3_s3.client import S3Client as BotoS3Client
from botocore.exceptions import ClientError, NoCredentialsError
from loguru import logger

class S3Client:
    """
    Handles interactions with AWS S3, including client initialization
    with assumed role credentials and file downloads.
    """

    def __init__(
        self,
        region_name: str,
        credentials: Optional[Dict[str, Any]] = None,
    ):
        """
        Initializes the S3 client.

        Creates a Boto3 S3 client using either provided temporary credentials
        (obtained via STS AssumeRole) or the default Boto3 credential chain.
        Validates credentials upon creation.

        :param region_name: The AWS region name (e.g., 'eu-west-1').
        :param credentials: Optional dictionary containing temporary AWS
                            credentials ('AccessKeyId', 'SecretAccessKey',
                            'SessionToken'). If None, uses the default chain.
        :raises ClientError: If Boto3 client creation or validation fails.
        :raises NoCredentialsError: If no credentials can be found.
        :raises ValueError: If provided credentials dictionary is incomplete.
        :raises Exception: For other unexpected errors during initialization.
        """
        self._region_name = region_name
        self._credentials= credentials
        self._s3_client = self._create_boto3_client()
        logger.info(f"S3Client initialized successfully for region '{self._region_name}'.")

    def _create_boto3_client(self) -> BotoS3Client:
        """Creates and validates the underlying Boto3 S3 client."""
        client_args = {"region_name": self._region_name}

        if self._credentials:
            required_keys = {"AccessKeyId", "SecretAccessKey", "SessionToken"}
            if not required_keys.issubset(self._credentials):
                raise ValueError(
                    "Provided credentials dictionary is missing required keys. "
                    f"Required: {required_keys}, Got: {self._credentials.keys()}"
                )
            client_args.update({
                "aws_access_key_id": self._credentials["AccessKeyId"],
                "aws_secret_access_key": self._credentials["SecretAccessKey"],
                "aws_session_token": self._credentials["SessionToken"],
            })
            log_prefix = "assumed role credentials"
        else:
            log_prefix = "default credentials"

        logger.info(f"Attempting to create Boto3 S3 client with {log_prefix}.")

        try:
            s3_client = boto3.client("s3", **client_args)
            logger.debug(f"Attempting to validate {log_prefix} using sts:GetCallerIdentity.")
            sts_client = boto3.client("sts", **client_args) 
            identity_info = sts_client.get_caller_identity()
            caller_arn = identity_info.get('Arn', 'Unknown ARN')
            logger.info(f"Boto3 S3 client created and {log_prefix} validated successfully (Caller ARN: {caller_arn}).")
            return s3_client
        except (ClientError, NoCredentialsError) as e:
            error_msg = f"Failed to create or validate client with {log_prefix}: {e}"
            logger.error(error_msg)
            raise
        except Exception as e:
            error_msg = f"Unexpected error creating Boto3 client with {log_prefix}: {type(e).__name__}"
            logger.exception(error_msg)
            raise

    def validate_s3_path(self, s3_path: str) -> bool:
        """
        Validates if the provided string follows the 's3://bucket/key' format.
        """
        if not isinstance(s3_path, str) or not s3_path.startswith("s3://"):
            raise ValueError(f"Invalid S3 path format. Must be a string starting with 's3://'. Got: {s3_path}")
        path_without_prefix = s3_path[5:] # len("s3://")
        if not path_without_prefix:
             raise ValueError("S3 path cannot be empty after 's3://'.")
        bucket_name, _, _ = path_without_prefix.partition("/")
        if not bucket_name:
            raise ValueError(f"Could not extract bucket name from S3 path: {s3_path}")
        logger.debug(f"S3 path format validated: {s3_path}")
        return True

    def _parse_s3_path(self, s3_path: str) -> Tuple[str, str]:
        """
        Parses a validated S3 path string into bucket name and key.
        """
        self.validate_s3_path(s3_path)
        path_without_prefix = s3_path[5:]
        bucket_name, separator, key = path_without_prefix.partition("/")
        return bucket_name, key

    def download_file(self, s3_path: str, download_path: Optional[str] = None) -> str:
        """
        Downloads a file from S3.
        """
        temp_file_object = None
        temp_file_path = None
        target_path: str

        try:
            bucket_name, key = self._parse_s3_path(s3_path)
            if not key or key.endswith("/"):
                raise ValueError(f"Invalid S3 key for file download: '{key}'. Key cannot be empty or end with '/'.")

            if download_path:
                target_path = os.path.abspath(download_path)
                target_dir = os.path.dirname(target_path)
                if target_dir: 
                    try:
                        os.makedirs(target_dir, exist_ok=True)
                        logger.debug(f"Ensured download directory exists: {target_dir}")
                    except OSError as e:
                        raise FileNotFoundError(f"Could not create directory for download path {target_path}: {e}") from e
                logger.debug(f"Using specified download path: {target_path}")
            else:
                _, filename = os.path.split(key)
                _, suffix = os.path.splitext(filename)
                suffix = suffix or ".tmp"
                temp_file_object = tempfile.NamedTemporaryFile(mode="wb", suffix=suffix, delete=False)
                temp_file_path = temp_file_object.name 
                temp_file_object.close()
                target_path = temp_file_path
                logger.debug(f"Using temporary download path: {target_path}")


            logger.debug(f"Attempting S3 download: s3://{bucket_name}/{key} -> {target_path}")
            self._s3_client.download_file(Bucket=bucket_name, Key=key, Filename=target_path)
            logger.info(f"Successfully downloaded s3://{bucket_name}/{key} to: {target_path}")

            return target_path

        except ClientError as e:
            error_msg = f"S3 client error downloading from {s3_path}: {e.response.get('Error', {}).get('Code', 'Unknown')}"
            logger.error(f"{error_msg} - {e}")
            if temp_file_path:
                self._cleanup_temp_file(temp_file_path, f"S3 client error ({error_msg})")
            raise
        except (ValueError, FileNotFoundError) as e:
            logger.error(f"Error during download setup for {s3_path}: {e}")
            if temp_file_path:
                 self._cleanup_temp_file(temp_file_path, "Download setup error")
            raise
        except Exception as e:
            error_msg = f"Unexpected error downloading from {s3_path}: {type(e).__name__}"
            logger.exception(error_msg)
            if temp_file_path:
                self._cleanup_temp_file(temp_file_path, "Unexpected download error")
            raise

    def _cleanup_temp_file(self, file_path: Optional[str], reason: str) -> None:
        """Safely attempts to remove a temporary file if it exists."""
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.debug(f"Removed temporary file created during failed download ({reason}): {file_path}")
            except OSError as clean_e:
                logger.warning(f"Could not remove temporary file {file_path} after error ({reason}): {clean_e}")

    def check_object_exists(self, s3_path: str) -> bool:
        """Checks if an S3 object exists at the specified path."""
        try:
            bucket_name, key = self._parse_s3_path(s3_path)
            if not key:
                raise ValueError("S3 key cannot be empty for checking existence.")
            self._s3_client.head_object(Bucket=bucket_name, Key=key)
            logger.debug(f"Object exists at s3://{bucket_name}/{key}")
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                logger.debug(f"Object does not exist at {s3_path}")
                return False
            else:
                logger.error(f"Error checking existence for {s3_path}: {e}")
                raise
        except ValueError as e:
            logger.error(f"Invalid path for existence check: {e}")
            raise
        except Exception as e:
            logger.exception(f"Unexpected error checking existence for {s3_path}: {type(e).__name__}")
            raise

    def read_object_content(self, s3_path: str, encoding: Optional[str] = 'utf-8') -> bytes | str:
        """Reads S3 object content into memory as bytes or string."""
        try:
            bucket_name, key = self._parse_s3_path(s3_path)
            if not key:
                raise ValueError("S3 key cannot be empty for reading content.")
            response = self._s3_client.get_object(Bucket=bucket_name, Key=key)
            content_bytes = response['Body'].read()
            logger.debug(f"Read {len(content_bytes)} bytes from s3://{bucket_name}/{key}")
            if encoding:
                try:
                    return content_bytes.decode(encoding)
                except UnicodeDecodeError as decode_error:
                    logger.error(f"Failed to decode content from {s3_path} using {encoding}: {decode_error}")
                    raise ValueError(f"Could not decode content with encoding '{encoding}'") from decode_error
            else:
                return content_bytes
        except ClientError as e:
            logger.error(f"Error reading content from {s3_path}: {e}")
            raise
        except ValueError as e:
            logger.error(f"Invalid path/key or decoding error for {s3_path}: {e}")
            raise
        except Exception as e:
            logger.exception(f"Unexpected error reading content from {s3_path}: {type(e).__name__}")
            raise

    def list_objects(self, s3_path_prefix: str, max_keys: Optional[int] = 1000) -> list[str]:
        """Lists object keys matching the specified S3 prefix."""
        object_keys = []
        try:
            bucket_name, prefix = self._parse_s3_path(s3_path_prefix)
            paginator = self._s3_client.get_paginator('list_objects_v2')
            page_iterator = paginator.paginate(
                Bucket=bucket_name,
                Prefix=prefix,
                PaginationConfig={'MaxItems': max_keys}
            )
            for page in page_iterator:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        if not obj['Key'].endswith('/'):
                           object_keys.append(f"s3://{bucket_name}/{obj['Key']}")
            logger.debug(f"Listed {len(object_keys)} objects under prefix '{s3_path_prefix}'")
            return object_keys
        except ClientError as e:
            logger.error(f"Error listing objects for prefix {s3_path_prefix}: {e}")
            raise
        except ValueError as e:
             logger.error(f"Invalid path prefix for listing: {e}")
             raise
        except Exception as e:
            logger.exception(f"Unexpected error listing objects for prefix {s3_path_prefix}: {type(e).__name__}")
            raise

    def upload_file(self, local_path: str, s3_path: str) -> None:
        """Uploads a local file to the specified S3 path."""
        if not os.path.exists(local_path):
            raise FileNotFoundError(f"Local file not found: {local_path}")
        if not os.path.isfile(local_path):
            raise ValueError(f"Local path must be a file: {local_path}")

        try:
            bucket_name, key = self._parse_s3_path(s3_path)
            if not key or key.endswith('/'):
                 raise ValueError("S3 key cannot be empty or end with '/' for upload.")
            logger.debug(f"Attempting to upload {local_path} to s3://{bucket_name}/{key}")
            self._s3_client.upload_file(Filename=local_path, Bucket=bucket_name, Key=key)
            logger.info(f"Successfully uploaded {local_path} to s3://{bucket_name}/{key}")
        except ClientError as e:
            logger.error(f"Error uploading file {local_path} to {s3_path}: {e}")
            raise
        except ValueError as e:
            logger.error(f"Invalid S3 path/key for upload: {e}")
            raise
        except Exception as e:
            logger.exception(f"Unexpected error uploading {local_path} to {s3_path}: {type(e).__name__}")
            raise